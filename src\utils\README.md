# API请求工具使用说明

本项目提供了一套完整的API请求解决方案，基于axios封装，包含请求拦截、响应处理、认证管理等功能。

## 工具组成

1. **request.js** - 核心请求工具，封装axios实例和拦截器
2. **auth.js** - 认证相关工具，处理token的存储和获取

## request.js 功能特点

- 统一的请求拦截与响应拦截
- 自动添加认证token
- 动态URL拼接和环境配置
- 请求签名生成，提高安全性
- 加载状态自动控制
- 统一的错误处理机制
- 支持多种请求方式（GET、POST、PUT、DELETE）
- 文件下载功能

## 使用方法

### 基础使用

**request工具支持两种调用方式：**

#### 方式一：函数式调用（推荐）

```javascript
import request from '@/utils/request';
import util from '@/util/util';

// GET请求
request.get('/api/test', {
  params: { id: 123 },
  showLoading: true
}).then(res => {
  // 处理成功响应
  if (res.success) {
    console.log('请求成功', res.data);
  }
}).catch(error => {
  console.error('请求失败', error);
});

// POST请求
request.post('/api/submit', {
  name: '张三',
  age: 25
}, {
  showLoading: true,
  loadingText: '提交中...'
}).then(res => {
  // 处理成功响应
}).catch(error => {
  // 处理错误
});
```

#### 方式二：对象式调用

```javascript
import request from '@/utils/request';

// GET请求
request({
  url: '/api/test',
  method: 'get',
  params: { id: 123 },
  showLoading: true
}).then(res => {
  // 处理成功响应
});

// POST请求
request({
  url: '/api/submit',
  method: 'post',
  data: {
    name: '张三',
    age: 25
  },
  showLoading: true,
  loadingText: '提交中...'
}).then(res => {
  // 处理成功响应
});

// 在API文件中定义
function getAccountList(data) {
  return request({
    url: '/sys/user/search',
    method: 'post',
    data
  })
}

### 请求配置选项

```javascript
request.get('/api/test', {
  // 请求参数
  params: { id: 123 },
  // 是否显示加载提示
  showLoading: true,
  // 加载提示文本
  loadingText: '加载中...',
  // 是否显示错误提示
  showErrorToast: true,
  // 自定义请求头
  headers: {
    'Content-Type': 'application/json'
  },
  // 超时时间（毫秒）
  timeout: 30000,
  // 响应类型
  responseType: 'json'
});
```

### 文件下载

```javascript
request.downloadFile('/api/download', {
  showLoading: true,
  loadingText: '下载中...',
  fileName: 'example.pdf', // 指定下载文件名
  params: { id: 123 } // 下载参数
});
```

### 请求取消

```javascript
// 创建取消令牌
const CancelToken = axios.CancelToken;
const source = CancelToken.source();

// 发起请求
request.get('/api/longRequest', {
  cancelToken: source.token
});

// 取消请求
setTimeout(() => {
  source.cancel('请求被用户取消');
}, 5000);
```

## auth.js 功能说明

```javascript
import { getToken, setToken, removeToken, getUserId, setUserId, removeUserId } from '@/utils/auth';

// 获取token
token = getToken();

// 设置token（支持记住密码功能）
setToken('your_token_here', true); // 第二个参数为true表示记住密码

// 移除token
removeToken();

// 获取用户ID
userId = getUserId();

// 设置用户ID
setUserId('user_123');

// 移除用户ID
removeUserId();
```

## API调用最佳实践

### 1. 按模块组织API接口

建议在`src/api/`目录下按模块创建API文件，例如：

```javascript
// src/api/user.js
import request from '@/utils/request';

/**
 * 用户登录
 */
export const login = (data) => {
  return request.post('/auth/login', data, {
    showLoading: true,
    loadingText: '登录中...'
  });
};

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
  return request.get('/user/info');
};
```

### 2. 在Vue组件中使用

```javascript
import { login } from '@/api/user';

export default {
  methods: {
    async handleLogin() {
      try {
        const res = await login({
          username: this.username,
          password: this.password
        });
        
        if (res.success) {
          // 处理登录成功逻辑
          this.$router.push('/home');
        }
      } catch (error) {
        console.error('登录失败', error);
        // 可以在这里添加自定义错误处理
      }
    }
  }
};
```

### 3. 在Vuex中使用

```javascript
import { login } from '@/api/user';

export default {
  state: { ... },
  mutations: { ... },
  actions: {
    async userLogin({ commit }, loginData) {
      try {
        const res = await login(loginData);
        
        if (res.success && res.data.token) {
          // 保存token和用户信息
          commit('SET_TOKEN', res.data.token);
          commit('SET_USER_INFO', res.data.userInfo);
        }
        
        return res;
      } catch (error) {
        throw error;
      }
    }
  }
};
```

## 认证机制

1. **token获取**：登录成功后从服务器获取token
2. **token存储**：使用`auth.js`中的方法存储token（支持sessionStorage和localStorage）
3. **token传递**：请求拦截器自动将token添加到请求头
4. **token验证**：响应拦截器处理token过期等情况
5. **token清除**：登出时清除存储的token

## 错误处理

系统会自动处理以下错误情况：

- **401未授权**：跳转到登录页面
- **503服务不可用**：显示"服务繁忙，请稍后再试"提示
- **429请求过多**：显示"请求过于频繁，请稍后再试"提示
- **598超时**：显示"请求超时，请检查网络"提示
- **其他错误**：显示具体错误信息或默认错误提示

## 性能优化

1. **请求去重**：对于相同的请求，避免短时间内重复发送
2. **请求缓存**：对于不经常变化的数据，可以考虑添加缓存机制
3. **请求重试**：对临时性网络错误实现自动重试
4. **合理设置超时**：根据接口特性设置合适的超时时间

## 注意事项

1. 所有API接口应使用HTTPS协议
2. 敏感数据传输前应进行加密处理
3. 避免在前端存储敏感信息（如密码）
4. 长时间未操作后，应提示用户重新登录
5. 对于重要操作，建议添加二次确认