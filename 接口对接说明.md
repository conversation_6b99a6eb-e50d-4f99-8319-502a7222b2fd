# 活动页面接口对接说明

## 修改概述

已完成页面活动对应接口与接口文档的对应关系，并使用项目中的request方式替代http方式。

## 修改的文件

### 1. src/api/activity.js
- 将http导入改为request导入
- 更新所有接口地址为新的接口文档地址
- 修改请求方式为POST（除活动详情为GET）

**主要接口映射：**
- `getActivityList` → `/activity-manager-api/app/activity/getByTypeId` (1.6 首页的活动列表)
- `getActivityDetail` → `/activity-manager-api/app/activity/detail` (1.5 活动详情)
- `searchActivity` → `/activity-manager-api/app/activity/searchAct` (1.8 首页搜索中间页)
- `chooseActivity` → `/activity-manager-api/app/activity/chooseAct` (1.7 首页筛选活动)
- `registerActivity` → `/activity-manager-api/app/activity/register` (1.9 一键报名)

### 2. src/views/activity/detail.vue
**活动详情页面修改：**
- 对接1.5活动详情接口
- 数据字段映射：
  - `actTitle` → `activity.title`
  - `headerImg` → `activity.imageUrl`
  - `activityStatus` → `activity.activityStatus`
  - `actTime` → `activity.activityTime`
  - `locationString` → `activity.location`
  - `actNotice` → `activity.notice`
  - `actDesc` → `activity.description`

**报名表单扩展：**
- 添加更多报名字段：证件类型、身份证号、性别、年龄、携带成人数、携带儿童数、身高、体重、学历、社区、地址
- 对接1.9一键报名接口
- 完整的表单验证和数据提交

### 3. src/views/home/<USER>
**首页活动列表修改：**
- 智能选择接口：有筛选条件时使用1.7筛选接口，无筛选时使用1.6列表接口
- 数据字段映射：
  - `id` → `activity.id`
  - `actTitle` → `activity.title`
  - `headerImg` → `activity.imageUrl`
  - `activityStatus` → `activity.activityStatus`
  - `actTime` → `activity.activityTime`
  - `registerTime` → `activity.registrationTime`
  - `numRage` → `activity.maxParticipants`
  - `registrantsNum` → `activity.participantCount`

**筛选功能：**
- 对接1.7首页筛选活动接口
- 支持按活动类型、活动状态、报名状态筛选

### 4. src/views/search/search.vue
**搜索页面修改：**
- 对接1.8首页搜索中间页接口
- 支持按活动名称搜索
- 数据字段映射与首页保持一致
- 添加状态转换方法

## 接口参数说明

### 1.5 活动详情
```javascript
// 入参
{ actId: Long } // 活动Id

// 出参映射
actTitle → title
headerImg → imageUrl
activityStatus → activityStatus
actTime → activityTime
locationString → location
actNotice → notice
actDesc → description
```

### 1.6 首页活动列表
```javascript
// 入参
{
  typeId: Long,        // 类型Id
  popularity: Integer, // 1选择人气排行，0不选
  selectSort: Integer  // 排序方式，1升序，0降序
}

// 出参映射
id → id
actTitle → title
headerImg → imageUrl
activityStatus → activityStatus
actTime → activityTime
registerTime → registrationTime
numRage → maxParticipants
registrantsNum → participantCount
```

### 1.7 首页筛选活动
```javascript
// 入参
{
  typeId: Long,           // 类型Id (可选)
  activityStatus: String, // 活动状态 (可选)
  registerStatus: String  // 活动报名状态 (可选)
}
// 出参与1.6相同
```

### 1.8 首页搜索中间页
```javascript
// 入参
{
  actId: Long,      // 活动Id (可选)
  actTitle: String  // 活动名称 (可选)
}

// 出参映射
actTitle → title
headerImg → imageUrl
activityStatus → activityStatus
actTime → activityTime
register → userRegistered (1已报名，0未报名)
numRage → maxParticipants
registerCount → participantCount
```

### 1.9 一键报名
```javascript
// 入参
{
  id: Long,                        // 活动Id (可选)
  name: String,                    // 姓名 (必填)
  phone: String,                   // 手机号 (必填)
  cardType: String,                // 证件类型 (可选)
  idCard: String,                  // 身份证号 (可选)
  gender: String,                  // 性别 (可选)
  age: String,                     // 年龄 (可选)
  human: Integer,                  // 携带成人数 (可选)
  child: Integer,                  // 携带儿童数 (可选)
  high: Double,                    // 身高 (可选)
  weight: Double,                  // 体重 (可选)
  educate: String,                 // 学历 (可选)
  community: String,               // 社区 (可选)
  address: String,                 // 地址 (可选)
  selfAdds: List<Map<String,Object>> // 自增项目 (可选)
}
```

## 状态转换

添加了统一的状态转换方法：
- `getActivityStatusText()` - 将状态码转换为中文显示
- `getStatusClass()` - 将状态码转换为CSS类名

支持的状态：
- `not_started` / `0` → 未开始
- `ongoing` / `1` → 进行中  
- `ended` / `2` → 已结束

## 注意事项

1. 所有接口都使用项目统一的request方式，支持完整的错误处理和loading状态
2. 保留了原有的模拟数据作为接口调用失败时的降级方案
3. 添加了完整的数据字段映射，确保页面显示正常
4. 扩展了报名表单，支持接口文档中的所有字段
5. 智能选择接口，根据是否有筛选条件自动选择合适的接口

## 测试建议

1. 测试活动详情页面的数据加载和显示
2. 测试首页活动列表的加载和筛选功能
3. 测试搜索功能的关键词搜索
4. 测试报名表单的完整流程
5. 测试接口调用失败时的降级处理
