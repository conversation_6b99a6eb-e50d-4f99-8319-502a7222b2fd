/*
 * @Author: Activity H5 Project
 * @Date: 2025-01-01
 * @Description: 活动相关API接口
 */

import http from './http';

// 获取首页banner列表
export const getBannerList = (params = {}) => {
  return http.get('/api/banner/list', { params });
};

// 获取精选活动列表
export const getFeaturedList = (params = {}) => {
  return http.get('/api/activity/featured', { params });
};

// 获取活动分类列表
export const getCategoryList = (params = {}) => {
  return http.get('/api/activity/category', { params });
};

// 获取活动列表
export const getActivityList = (params = {}) => {
  return http.get('/api/activity/list', { params });
};

// 获取活动详情
export const getActivityDetail = (id) => {
  return http.get(`/api/activity/detail/${id}`);
};

// 搜索活动
export const searchActivity = (params = {}) => {
  return http.get('/api/activity/search', { params });
};

// 获取热门搜索关键词
export const getHotSearchList = () => {
  return http.get('/api/search/hot');
};

// 活动报名
export const registerActivity = (data) => {
  return http.post('/api/activity/register', data);
};

// 取消报名
export const cancelRegister = (activityId) => {
  return http.post(`/api/activity/cancel/${activityId}`);
};

// 收藏活动
export const favoriteActivity = (activityId) => {
  return http.post(`/api/activity/favorite/${activityId}`);
};

// 取消收藏
export const unfavoriteActivity = (activityId) => {
  return http.delete(`/api/activity/favorite/${activityId}`);
};

// 获取我的活动列表
export const getMyActivityList = (params = {}) => {
  return http.get('/api/user/activities', { params });
};

// 获取活动统计数据
export const getActivityStats = () => {
  return http.get('/api/user/activity-stats');
};

// 获取运营位配置
export const getOperationConfig = (position) => {
  return http.get(`/api/operation/config/${position}`);
};
