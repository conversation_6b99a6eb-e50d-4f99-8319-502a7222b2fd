/**
 * @Author: Activity H5 Project
 * @Date: 2025-01-01
 * @Description: 活动相关API接口
 */

import request from '@/utils/request';
import mockData from './mockData';

// 开关：true使用mock数据，false使用真实API
const USE_MOCK = true;


// 获取Banner列表
export const getBannerList = () => {
  if (USE_MOCK) {
    return mockData.getBannerList();
  }
  return request('/app/banner/list', {}, 'get');
};

// 获取精选活动列表
export const getFeaturedActivityList = () => {
  if (USE_MOCK) {
    return mockData.getFeaturedActivityList();
  }
  return request('/app/activity/featuredList', {}, 'get');
};

// 获取活动类型列表
export const getActivityCategoryList = () => {
  if (USE_MOCK) {
    return mockData.getActivityCategoryList();
  }
  return request('/app/activityType/list', {}, 'get');
};

// 1.6 首页的活动列表
export const getActivityList = (params = {}) => {
  if (USE_MOCK) {
    return mockData.getActivityList(params);
  }
  return request('/app/activity/getByTypeId', params, 'post');
};

// 1.5 活动详情
export const getActivityDetail = (actId) => {
  if (USE_MOCK) {
    return mockData.getActivityDetail(actId);
  }
  return request('/app/activity/detail', { actId }, 'get');
};

// 1.8 首页搜索中间页
export const searchActivity = (params = {}) => {
  if (USE_MOCK) {
    return mockData.searchActivity(params);
  }
  return request('/app/activity/searchAct', params, 'post');
};

// 1.7 首页筛选活动
export const chooseActivity = (params = {}) => {
  if (USE_MOCK) {
    return mockData.chooseActivity(params);
  }
  return request('/app/activity/chooseAct', params, 'post');
};


// 1.9 一键报名
export const registerActivity = (data) => {
  if (USE_MOCK) {
    return mockData.registerActivity(data);
  }
  return request('/app/activity/register', data, 'post');
};
