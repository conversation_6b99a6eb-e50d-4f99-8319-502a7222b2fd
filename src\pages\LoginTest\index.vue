<template>
  <div class="login-test-page">
    <h1>登录功能测试页面</h1>
    
    <div class="test-section">
      <h2>当前登录状态</h2>
      <div class="status-info">
        <p><strong>用户ID:</strong> {{ userId || '未登录' }}</p>
        <p><strong>登录状态:</strong> {{ isLoggedIn ? '已登录' : '未登录' }}</p>
      </div>
    </div>
    
    <div class="test-section">
      <h2>操作按钮</h2>
      <div class="button-group">
        <button class="test-btn" @click="goToLogin">前往登录页面</button>
        <button class="test-btn" @click="logout" v-if="isLoggedIn">退出登录</button>
        <button class="test-btn" @click="checkLoginStatus">检查登录状态</button>
      </div>
    </div>
    
    <div class="test-section">
      <h2>登录页面功能说明</h2>
      <div class="feature-list">
        <h3>已实现的功能：</h3>
        <ul>
          <li>✅ 手机号输入（3-3-4格式化显示）</li>
          <li>✅ 手机号正则校验</li>
          <li>✅ 获取验证码按钮（60秒倒计时）</li>
          <li>✅ 验证码输入（6位数字）</li>
          <li>✅ 隐私协议勾选</li>
          <li>✅ 登录按钮状态控制</li>
          <li>✅ 自定义数字键盘</li>
          <li>✅ 输入框清除功能</li>
          <li>✅ Toast轻提示</li>
          <li>✅ 隐私协议弹窗</li>
        </ul>
        
        <h3>页面特性：</h3>
        <ul>
          <li>📱 响应式设计，适配移动端</li>
          <li>🎨 符合设计稿的UI样式</li>
          <li>⌨️ 自定义数字键盘（仿iOS风格）</li>
          <li>🔒 完整的表单验证逻辑</li>
          <li>🚀 与现有项目架构完全兼容</li>
        </ul>
      </div>
    </div>
    
    <div class="test-section">
      <h2>API接口说明</h2>
      <div class="api-info">
        <p><strong>发送验证码:</strong> POST /api/auth/sms/send</p>
        <p><strong>短信登录:</strong> POST /api/auth/login/sms</p>
        <p><strong>获取隐私协议:</strong> GET /api/policy/privacy</p>
        <p class="note">注意：这些接口需要后端配合实现</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoginTestPage',
  data() {
    return {
      userId: '',
      isLoggedIn: false
    };
  },
  mounted() {
    this.checkLoginStatus();
  },
  methods: {
    // 前往登录页面
    goToLogin() {
      this.$router.push('/login');
    },
    
    // 检查登录状态
    checkLoginStatus() {
      this.userId = localStorage.getItem('userId') || '';
      this.isLoggedIn = !!this.userId;
    },
    
    // 退出登录
    logout() {
      localStorage.removeItem('userId');
      localStorage.removeItem('userInfo');
      this.checkLoginStatus();
      this.$toast('已退出登录');
    }
  }
};
</script>

<style lang="scss" scoped>
.login-test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.test-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h2 {
    color: #333;
    margin-bottom: 15px;
    border-bottom: 2px solid #007aff;
    padding-bottom: 5px;
  }
  
  h3 {
    color: #666;
    margin: 15px 0 10px 0;
  }
}

.status-info {
  p {
    margin: 8px 0;
    font-size: 16px;
    
    strong {
      color: #333;
    }
  }
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 10px 20px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    background: #0056cc;
  }
  
  &:active {
    transform: translateY(1px);
  }
}

.feature-list {
  ul {
    margin: 10px 0;
    padding-left: 20px;
    
    li {
      margin: 5px 0;
      line-height: 1.5;
    }
  }
}

.api-info {
  p {
    margin: 8px 0;
    font-family: 'Courier New', monospace;
    background: #f5f5f5;
    padding: 8px;
    border-radius: 4px;
    
    &.note {
      background: #fff3cd;
      color: #856404;
      font-family: inherit;
    }
  }
}
</style>
