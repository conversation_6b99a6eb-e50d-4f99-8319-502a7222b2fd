# 登录功能快速启动指南

## 🚀 快速体验

### 1. 启动项目
```bash
npm run serve
# 或
yarn serve
```

### 2. 访问页面
- **登录页面**: http://localhost:8080/login
- **测试页面**: http://localhost:8080/login-test

### 3. 功能演示

#### 在登录页面可以体验：
1. **手机号输入**: 输入手机号，自动格式化为 3-3-4 格式
2. **获取验证码**: 输入11位手机号后，按钮变为可点击状态
3. **验证码输入**: 输入6位验证码
4. **自定义键盘**: 点击输入框弹出数字键盘
5. **隐私协议**: 勾选协议复选框
6. **登录按钮**: 所有条件满足后按钮高亮可点击

#### 在测试页面可以：
1. 查看当前登录状态
2. 跳转到登录页面
3. 查看功能说明文档

## 📱 移动端测试

### 方法1: 浏览器开发者工具
1. 打开Chrome开发者工具 (F12)
2. 点击设备模拟器图标
3. 选择移动设备 (如iPhone X)
4. 访问登录页面

### 方法2: 手机浏览器
1. 确保手机和电脑在同一网络
2. 查看电脑IP地址
3. 手机浏览器访问: http://[电脑IP]:8080/login

## 🔧 自定义配置

### 修改API地址
在 `.env` 文件中设置：
```
VUE_APP_API_BASE_URL=https://your-api-domain.com
```

### 修改品牌信息
编辑 `src/pages/Login/index.vue` 中的品牌展示区域：
```vue
<div class="brand-name">你的品牌名称</div>
```

## 🎯 核心文件说明

- `src/pages/Login/index.vue` - 登录页面主组件
- `src/api/auth.js` - 登录相关API接口
- `src/pages/LoginTest/index.vue` - 功能测试页面
- `LOGIN_IMPLEMENTATION.md` - 详细实现文档

## ⚠️ 注意事项

1. **后端接口**: 当前为前端实现，需要后端配合提供真实接口
2. **验证码**: 目前为模拟发送，实际需要短信服务
3. **登录状态**: 使用localStorage存储，生产环境建议使用更安全的方式

## 🐛 常见问题

### Q: 点击获取验证码没有反应？
A: 检查手机号是否为11位有效号码，确保符合正则表达式 `/^1[3-9]\d{9}$/`

### Q: 登录按钮一直是灰色？
A: 确保同时满足：手机号11位 + 验证码6位 + 勾选隐私协议

### Q: 自定义键盘不显示？
A: 点击输入框获得焦点后会自动显示，点击键盘外区域可关闭

## 📞 技术支持

如有问题，请查看：
1. `LOGIN_IMPLEMENTATION.md` - 详细技术文档
2. 浏览器控制台错误信息
3. 网络请求状态
