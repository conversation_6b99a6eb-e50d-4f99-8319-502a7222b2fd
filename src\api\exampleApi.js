// 示例API调用文件
import request from '@/utils/request';
import util from '@/util/util';

/**
 * 获取医生列表
 * @param {Object} params - 请求参数
 * @param {string} params.hospitalId - 医院ID
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {boolean} showLoading - 是否显示加载提示
 * @returns {Promise}
 */
export const getDoctorList = (params, showLoading = true) => {
  return request.get('/doctor/list', {
    params,
    showLoading
  });
};

/**
 * 获取活动详情
 * @param {string} id - 活动ID
 * @returns {Promise}
 */
export const getActivityDetail = (id) => {
  return request.get(`/activity/detail/${id}`, {
    showLoading: true,
    loadingText: '获取活动详情中...'
  });
};

/**
 * 提交表单数据
 * @param {Object} data - 表单数据
 * @returns {Promise}
 */
export const submitForm = (data) => {
  return request.post('/form/submit', data, {
    showLoading: true,
    loadingText: '提交中...'
  }).then(res => {
    if (res.success) {
      util.showToast('提交成功');
    }
    return res;
  });
};

/**
 * 上传文件
 * @param {FormData} formData - 表单数据（包含文件）
 * @returns {Promise}
 */
export const uploadFile = (formData) => {
  return request.post('/file/upload', formData, {
    showLoading: true,
    loadingText: '上传中...',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000 // 上传文件超时时间设为60秒
  });
};

/**
 * 下载文件
 * @param {string} fileUrl - 文件URL
 * @param {string} fileName - 文件名
 * @returns {Promise}
 */
export const downloadFile = (fileUrl, fileName) => {
  return request.downloadFile(fileUrl, {
    showLoading: true,
    loadingText: '下载中...',
    fileName
  });
};

/**
 * 批量请求示例（无loading）
 * @param {Array} ids - ID数组
 * @returns {Promise<Array>}
 */
export const batchGetInfo = async (ids) => {
  try {
    // 显示统一的加载提示
    util.showLoading('处理中...');
    
    // 使用Promise.all进行并行请求
    const promises = ids.map(id => 
      request.get(`/info/detail/${id}`, { showLoading: false })
    );
    
    const results = await Promise.all(promises);
    
    // 隐藏加载提示
    util.hideLoading();
    
    return results;
  } catch (error) {
    util.hideLoading();
    util.showToast('批量处理失败');
    throw error;
  }
};

/**
 * 微信登录示例（使用对象形式调用）
 * @param {Object} loginData - 登录数据
 * @returns {Promise}
 */
export const wxLogin = (loginData) => {
  return request({
    url: '/auth/wx/login',
    method: 'post',
    data: loginData,
    showLoading: true,
    loadingText: '登录中...'
  }).then(res => {
    if (res.success && res.data && res.data.token) {
      // 登录成功后保存token
      const { token, userInfo } = res.data;
      
      // 保存token到本地存储
      const isRemember = loginData.isRemember || false;
      if (isRemember) {
        localStorage.setItem('access_token', token);
      } else {
        sessionStorage.setItem('access_token', token);
      }
      
      // 保存用户信息
      if (userInfo) {
        localStorage.setItem('userInfo', JSON.stringify(userInfo));
      }
      
      util.showToast('登录成功');
    }
    return res;
  });
};

/**
 * 带重试机制的请求示例
 * @param {Object} params - 请求参数
 * @param {number} retryTimes - 重试次数（默认为3次）
 * @returns {Promise}
 */
export const requestWithRetry = async (params, retryTimes = 3) => {
  let lastError;
  
  for (let i = 0; i < retryTimes; i++) {
    try {
      const result = await request.get('/api/retry', {
        params,
        showLoading: i === 0 // 只在第一次显示loading
      });
      return result;
    } catch (error) {
      // 只在最后一次失败时显示错误提示
      if (i === retryTimes - 1) {
        util.showToast(`请求失败: ${error.message || '网络错误'}`);
      }
      lastError = error;
    }
  }
  
  throw lastError;
};

/**
 * 获取用户权限列表
 * @returns {Promise}
 */
export const getUserPermissions = () => {
  // 配置不显示默认的错误提示，因为这个接口失败后我们要做特殊处理
  return request.get('/user/permissions', {
    showLoading: true,
    showErrorToast: false
  }).catch(error => {
    // 自定义错误处理
    console.error('获取权限列表失败:', error);
    // 返回默认权限列表
    return {
      success: true,
      data: []
    };
  });
};