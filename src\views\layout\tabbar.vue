<template>
  <div class="layout-container">
    <!-- 主内容区域 -->
    <div class="main-content">
      <keep-alive>
        <router-view v-if="$route.meta.keepAlive" />
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive" :key="$route.fullPath" />
    </div>
    
    <!-- 底部导航栏 -->
    <van-tabbar v-model="activeTab" @change="handleTabChange" fixed>
      <van-tabbar-item name="home" icon="home-o">
        活动主页
      </van-tabbar-item>
      <van-tabbar-item name="profile" icon="user-o">
        个人中心
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
import Vue from 'vue';
import { Tabbar, TabbarItem } from 'vant';

Vue.use(Tabbar).use(TabbarItem);

export default {
  name: 'LayoutTabbar',
  data() {
    return {
      activeTab: 'home'
    };
  },
  watch: {
    $route: {
      handler(to) {
        // 根据路由更新当前激活的tab
        if (to.name === 'home') {
          this.activeTab = 'home';
        } else if (to.name === 'profile') {
          this.activeTab = 'profile';
        }
      },
      immediate: true
    }
  },
  methods: {
    handleTabChange(name) {
      if (name !== this.$route.name) {
        this.$router.push({ name });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  overflow: hidden;
  padding-bottom: 50px; // 为底部导航栏留出空间
}
</style>
