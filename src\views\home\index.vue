<template>
  <div class="home-container">
    <!-- 固定搜索栏 -->
    <div class="search-bar" :class="{ 'search-bar-scrolled': isScrolled }">
      <div class="search-input" @click="goToSearch">
        <van-icon name="search" />
        <span class="placeholder">请输入活动名称或编号进行搜索</span>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="content" @scroll="handleScroll" ref="content">
      <!-- Banner轮播 -->
      <div class="banner-section" v-if="bannerList.length > 0">
        <van-swipe class="banner-swipe" :autoplay="3000" indicator-color="white">
          <van-swipe-item v-for="(banner, index) in bannerList" :key="index" @click="handleBannerClick(banner)">
            <img :src="banner.imageUrl" :alt="banner.title" class="banner-image" />
          </van-swipe-item>
        </van-swipe>
      </div>

      <!-- 精选活动 -->
      <div class="featured-section" v-if="featuredList.length > 0">
        <h3 class="section-title">精选活动</h3>
        <div class="featured-grid">
          <div 
            v-for="(item, index) in featuredList.slice(0, 4)" 
            :key="index"
            class="featured-item"
            :class="`featured-item-${index + 1}`"
            @click="handleFeaturedClick(item)"
          >
            <img :src="item.imageUrl" :alt="item.title" class="featured-image" />
            <div class="featured-content">
              <h4 class="featured-title">{{ item.title }}</h4>
              <p class="featured-subtitle">{{ item.subtitle }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 活动类型 -->
      <div class="category-section">
        <van-tabs v-model="activeCategory" @click="handleCategoryChange" swipeable>
          <van-tab title="全部" name="all"></van-tab>
          <van-tab 
            v-for="category in categoryList" 
            :key="category.id"
            :title="category.name" 
            :name="category.id"
          >
            <template #title>
              <div class="category-tab">
                <img :src="category.icon" :alt="category.name" class="category-icon" />
                <span>{{ category.name }}</span>
              </div>
            </template>
          </van-tab>
        </van-tabs>
      </div>

      <!-- 排序和筛选 -->
      <div class="filter-section">
        <div class="sort-buttons">
          <button 
            class="sort-btn" 
            :class="{ active: sortType === 'time' }"
            @click="handleSort('time')"
          >
            最近发布
          </button>
          <button 
            class="sort-btn popularity-btn" 
            :class="{ active: sortType === 'popularity' }"
            @click="handleSort('popularity')"
          >
            人气排行
            <van-icon :name="popularityOrder === 'asc' ? 'arrow-up' : 'arrow-down'" v-if="sortType === 'popularity'" />
          </button>
        </div>
        <button 
          class="filter-btn" 
          :class="{ active: hasActiveFilter }"
          @click="showFilterPopup = true"
        >
          筛选
        </button>
      </div>

      <!-- 活动列表 -->
      <div class="activity-list">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="—我也是有底线的—"
          @load="loadActivityList"
        >
          <div
            v-for="activity in activityList"
            :key="activity.id"
            class="activity-item"
            @click="goToActivityDetail(activity)"
          >
            <!-- 活动标题 -->
            <div class="activity-title-wrapper">
              <h4 class="activity-title">{{ activity.title }}</h4>
              <div class="activity-code">活动编号：{{ activity.id }}</div>
            </div>

            <!-- 活动头图 -->
            <img :src="activity.imageUrl" :alt="activity.title" class="activity-image" />

            <!-- 活动信息 -->
            <div class="activity-info-section">
              <div class="info-row">
                <span class="info-label">活动状态：</span>
                <span class="activity-status" :class="activity.statusClass">{{ activity.statusText }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">报名时间：</span>
                <span class="info-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
              </div>
              <div class="info-row">
                <span class="info-label">活动时间：</span>
                <span class="info-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
              </div>
              <div class="info-row">
                <span class="info-label">报名人数：</span>
                <span class="info-value">{{ activity.participantCount }}/{{ getMaxParticipantsText(activity.maxParticipants) }} 人</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="activity-actions" v-if="shouldShowButton(activity)">
              <button
                class="action-button"
                :class="getButtonClass(activity)"
                @click.stop="handleActivityAction(activity)"
              >
                {{ getButtonText(activity) }}
              </button>
            </div>
          </div>
        </van-list>
      </div>
    </div>

    <!-- 筛选弹窗 -->
    <van-popup v-model="showFilterPopup" position="bottom" :style="{ height: '60%' }">
      <div class="filter-popup">
        <div class="filter-header">
          <h3>筛选条件</h3>
          <van-button type="default" size="small" @click="resetFilter">重置</van-button>
        </div>
        
        <div class="filter-content">
          <!-- 活动类型筛选 -->
          <div class="filter-group">
            <h4>活动类型</h4>
            <div class="filter-options">
              <van-button 
                v-for="category in allCategories" 
                :key="category.id"
                :type="filterForm.categoryId === category.id ? 'primary' : 'default'"
                size="small"
                @click="filterForm.categoryId = category.id"
              >
                {{ category.name }}
              </van-button>
            </div>
          </div>

          <!-- 活动状态筛选 -->
          <div class="filter-group">
            <h4>活动状态</h4>
            <div class="filter-options">
              <van-button 
                v-for="status in activityStatusOptions" 
                :key="status.value"
                :type="filterForm.activityStatus === status.value ? 'primary' : 'default'"
                size="small"
                @click="filterForm.activityStatus = status.value"
              >
                {{ status.label }}
              </van-button>
            </div>
          </div>

          <!-- 报名状态筛选 -->
          <div class="filter-group">
            <h4>活动报名状态</h4>
            <div class="filter-options">
              <van-button 
                v-for="status in registrationStatusOptions" 
                :key="status.value"
                :type="filterForm.registrationStatus === status.value ? 'primary' : 'default'"
                size="small"
                @click="filterForm.registrationStatus = status.value"
              >
                {{ status.label }}
              </van-button>
            </div>
          </div>
        </div>

        <div class="filter-footer">
          <van-button type="primary" block @click="applyFilter">确定</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue';
import { 
  Swipe, 
  SwipeItem, 
  Tabs, 
  Tab, 
  List, 
  Button, 
  Popup, 
  Icon,
  PullRefresh 
} from 'vant';

Vue.use(Swipe)
  .use(SwipeItem)
  .use(Tabs)
  .use(Tab)
  .use(List)
  .use(Button)
  .use(Popup)
  .use(Icon)
  .use(PullRefresh);

export default {
  name: 'Home',
  data() {
    return {
      isScrolled: false,
      bannerList: [],
      featuredList: [],
      categoryList: [],
      activityList: [],
      activeCategory: 'all',
      sortType: 'time', // 'time' | 'popularity'
      popularityOrder: 'desc', // 'asc' | 'desc'
      loading: false,
      finished: false,
      page: 1,
      showFilterPopup: false,
      filterForm: {
        categoryId: 'all',
        activityStatus: 'all',
        registrationStatus: 'all'
      },
      activityStatusOptions: [
        { label: '全部', value: 'all' },
        { label: '活动未开始', value: 'not_started' },
        { label: '活动进行中', value: 'ongoing' },
        { label: '活动已结束', value: 'ended' }
      ],
      registrationStatusOptions: [
        { label: '全部', value: 'all' },
        { label: '报名未开始', value: 'not_started' },
        { label: '报名进行中', value: 'ongoing' },
        { label: '报名已结束', value: 'ended' }
      ]
    };
  },
  computed: {
    allCategories() {
      return [{ id: 'all', name: '全部' }, ...this.categoryList];
    },
    hasActiveFilter() {
      return this.filterForm.categoryId !== 'all' || 
             this.filterForm.activityStatus !== 'all' || 
             this.filterForm.registrationStatus !== 'all';
    }
  },
  mounted() {
    this.initPage();
    this.bindScrollEvent();
  },
  methods: {
    async initPage() {
      await Promise.all([
        this.loadBannerList(),
        this.loadFeaturedList(),
        this.loadCategoryList()
      ]);
      this.loadActivityList();
    },
    
    bindScrollEvent() {
      this.$refs.content.addEventListener('scroll', this.handleScroll);
    },
    
    handleScroll(event) {
      const scrollTop = event.target.scrollTop;
      this.isScrolled = scrollTop > 50;
    },
    
    async loadBannerList() {
      // TODO: 调用API获取banner数据
      this.bannerList = [];
    },
    
    async loadFeaturedList() {
      // TODO: 调用API获取精选活动数据
      this.featuredList = [];
    },
    
    async loadCategoryList() {
      // TODO: 调用API获取活动类型数据
      this.categoryList = [];
    },
    
    async loadActivityList() {
      if (this.loading) return;

      this.loading = true;
      try {
        // TODO: 调用API获取活动列表数据
        const params = {
          page: this.page,
          categoryId: this.activeCategory,
          sortType: this.sortType,
          sortOrder: this.sortType === 'popularity' ? this.popularityOrder : 'desc',
          ...this.filterForm
        };

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 模拟不同状态的活动数据
        const mockActivities = [
          {
            id: '000009',
            title: '活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称',
            imageUrl: require('@/images/img/background.png'),
            statusText: '未开始',
            statusClass: 'not-started',
            timeText: '未到报名时间',
            participantCount: 0,
            maxParticipants: 10,
            registrationStatus: 'not_started',
            userRegistered: false
          },
          {
            id: '000007',
            title: '活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称',
            imageUrl: require('@/images/img/background.png'),
            statusText: '进行中',
            statusClass: 'ongoing',
            timeText: '未到报名时间',
            participantCount: 0,
            maxParticipants: 'unlimited',
            registrationStatus: 'ongoing',
            userRegistered: false
          },
          {
            id: '000006',
            title: '活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称',
            imageUrl: require('@/images/img/background.png'),
            statusText: '已结束',
            statusClass: 'ended',
            timeText: '报名已结束',
            participantCount: 0,
            maxParticipants: 10,
            registrationStatus: 'ended',
            userRegistered: true
          }
        ];

        const newList = this.page === 1 ? mockActivities : [];

        if (this.page === 1) {
          this.activityList = newList;
        } else {
          this.activityList.push(...newList);
        }

        this.page++;
        this.finished = newList.length < 10; // 假设每页10条数据
      } catch (error) {
        console.error('加载活动列表失败:', error);
      } finally {
        this.loading = false;
      }
    },
    
    goToSearch() {
      this.$router.push('/search');
    },
    
    handleBannerClick(banner) {
      if (banner.linkType === 'activity') {
        this.goToActivityDetail({ id: banner.linkId });
      } else if (banner.linkType === 'h5') {
        window.open(banner.linkUrl);
      }
    },
    
    handleFeaturedClick(item) {
      if (item.linkType === 'activity') {
        this.goToActivityDetail({ id: item.linkId });
      } else if (item.linkType === 'h5') {
        window.open(item.linkUrl);
      }
    },
    
    handleCategoryChange(name) {
      this.activeCategory = name;
      this.resetList();
    },
    
    handleSort(type) {
      if (type === 'popularity' && this.sortType === 'popularity') {
        // 切换升序/降序
        this.popularityOrder = this.popularityOrder === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortType = type;
        if (type === 'popularity') {
          this.popularityOrder = 'desc';
        }
      }
      this.resetList();
    },
    
    resetFilter() {
      this.filterForm = {
        categoryId: 'all',
        activityStatus: 'all',
        registrationStatus: 'all'
      };
    },
    
    applyFilter() {
      this.showFilterPopup = false;
      // 如果筛选了活动类型，需要同步到tab
      if (this.filterForm.categoryId !== 'all') {
        this.activeCategory = this.filterForm.categoryId;
      }
      this.resetList();
    },
    
    resetList() {
      this.page = 1;
      this.finished = false;
      this.activityList = [];
      this.loadActivityList();
    },
    
    goToActivityDetail(activity) {
      this.$router.push(`/activity/${activity.id}`);
    },

    getMaxParticipantsText(maxParticipants) {
      return maxParticipants === 'unlimited' ? '不限' : maxParticipants;
    },

    shouldShowButton(activity) {
      // 报名未开始或已结束时隐藏按钮
      return activity.registrationStatus !== 'not_started' &&
             activity.registrationStatus !== 'ended';
    },

    getButtonClass(activity) {
      if (activity.userRegistered) {
        return 'registered';
      }
      return 'primary';
    },

    getButtonText(activity) {
      if (activity.userRegistered) {
        return '已报名';
      }
      return '一键报名';
    },

    handleActivityAction(activity) {
      if (activity.userRegistered) {
        // 已报名，跳转到报名详情页
        this.$router.push(`/registration-detail/${activity.id}`);
      } else {
        // 未报名，跳转到活动详情页进行报名
        this.goToActivityDetail(activity);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.home-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.search-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 12px 16px;
  background: transparent;
  transition: background-color 0.3s ease;
  
  &.search-bar-scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }
  
  .search-input {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 24px;
    
    .van-icon {
      margin-right: 8px;
      color: #999;
    }
    
    .placeholder {
      color: #999;
      font-size: 14px;
    }
  }
}

.content {
  flex: 1;
  overflow-y: auto;
  padding-top: 60px; // 为固定搜索栏留出空间
}

.banner-section {
  .banner-swipe {
    height: 200px;
    
    .banner-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.featured-section {
  padding: 16px;
  
  .section-title {
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: bold;
  }
  
  .featured-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    
    .featured-item {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .featured-image {
        width: 100%;
        height: 120px;
        object-fit: cover;
      }
      
      .featured-content {
        padding: 12px;
        
        .featured-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .featured-subtitle {
          font-size: 12px;
          color: #666;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

.category-section {
  .category-tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .category-icon {
      width: 24px;
      height: 24px;
      margin-bottom: 4px;
    }
  }
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  
  .sort-buttons {
    display: flex;
    gap: 16px;
    
    .sort-btn {
      background: none;
      border: none;
      color: #666;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 4px;
      
      &.active {
        color: #1989fa;
        font-weight: bold;
      }
    }
  }
  
  .filter-btn {
    background: none;
    border: 1px solid #ddd;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    color: #666;
    
    &.active {
      background: #1989fa;
      color: white;
      border-color: #1989fa;
    }
  }
}

.activity-list {
  padding: 0 16px 16px;

  .activity-item {
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .activity-title-wrapper {
      padding: 16px 16px 8px;
      border-bottom: 1px solid #f0f0f0;

      .activity-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 4px;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .activity-code {
        font-size: 12px;
        color: #999;
      }
    }

    .activity-image {
      width: 100%;
      height: 160px;
      object-fit: cover;
    }

    .activity-info-section {
      padding: 16px;

      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;

        .info-label {
          color: #666;
          min-width: 80px;
        }

        .info-value {
          color: #333;
          flex: 1;
        }

        .activity-status {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;

          &.not-started {
            background: #f0f0f0;
            color: #666;
          }

          &.ongoing {
            background: #e8f5e8;
            color: #52c41a;
          }

          &.ended {
            background: #fff2e8;
            color: #fa8c16;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .activity-actions {
      padding: 0 16px 16px;

      .action-button {
        width: 100%;
        height: 44px;
        border-radius: 22px;
        border: none;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;

        &.primary {
          background: linear-gradient(135deg, #d4af37, #b8941f);
          color: white;
        }

        &.registered {
          background: #f0f0f0;
          color: #999;
          border: 1px solid #ddd;
        }
      }
    }
  }
}

.filter-popup {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      font-size: 18px;
      font-weight: bold;
    }
  }
  
  .filter-content {
    flex: 1;
    
    .filter-group {
      margin-bottom: 24px;
      
      h4 {
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: bold;
      }
      
      .filter-options {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
  }
  
  .filter-footer {
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
