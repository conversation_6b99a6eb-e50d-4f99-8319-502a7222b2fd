<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        
        .activity-detail {
            background: #f5f5f5;
            padding-bottom: 80px;
        }
        
        .detail-content {
            background: white;
            margin: 16px;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .activity-title-section {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .scrolling-title {
            height: 24px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .title-text {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            white-space: nowrap;
            display: inline-block;
            animation: scroll-text 10s linear infinite;
        }
        
        @keyframes scroll-text {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        .activity-code {
            font-size: 14px;
            color: #999;
        }
        
        .activity-header img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #ddd;
        }
        
        .activity-status-section {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .status-item {
            display: flex;
            align-items: center;
        }
        
        .status-label {
            font-size: 14px;
            color: #666;
            margin-right: 8px;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .status-badge.not-started {
            background: #f0f0f0;
            color: #666;
        }
        
        .status-badge.ongoing {
            background: #e8f5e8;
            color: #52c41a;
        }
        
        .status-badge.ended {
            background: #fff2e8;
            color: #fa8c16;
        }
        
        .activity-time-section {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .time-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .time-label {
            color: #666;
            min-width: 80px;
        }
        
        .time-value {
            color: #333;
            flex: 1;
        }
        
        .participant-section {
            padding: 16px;
        }
        
        .participant-text {
            font-size: 14px;
            color: #666;
        }
        
        .bottom-actions {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            padding: 12px 16px;
            background: white;
            border-top: 1px solid #f0f0f0;
        }
        
        .register-button {
            width: 100%;
            height: 48px;
            border-radius: 24px;
            border: none;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .register-button.primary {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            color: white;
        }
        
        .register-button.registered {
            background: #f0f0f0;
            color: #999;
            border: 1px solid #ddd;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            text-align: center;
            color: #333;
        }
        
        .demo-item {
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-section">
            <h2 class="demo-title">活动详情页面预览</h2>
            
            <!-- 未开始状态 -->
            <div class="demo-item">
                <div class="activity-detail">
                    <div class="detail-content">
                        <div class="activity-title-section">
                            <div class="scrolling-title">
                                <span class="title-text">活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称</span>
                            </div>
                            <div class="activity-code">活动编号：000009</div>
                        </div>
                        
                        <div class="activity-header">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzc1IiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDM3NSAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzNzUiIGhlaWdodD0iMjAwIiBmaWxsPSIjRERERERGIi8+Cjx0ZXh0IHg9IjE4Ny41IiB5PSIxMDAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuS4u+WKqOWbvueJhzwvdGV4dD4KPC9zdmc+" alt="活动图片" />
                        </div>
                        
                        <div class="activity-status-section">
                            <div class="status-item">
                                <span class="status-label">活动状态：</span>
                                <span class="status-badge not-started">未开始</span>
                            </div>
                        </div>
                        
                        <div class="activity-time-section">
                            <div class="time-item">
                                <span class="time-label">报名时间：</span>
                                <span class="time-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
                            </div>
                            <div class="time-item">
                                <span class="time-label">活动时间：</span>
                                <span class="time-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
                            </div>
                        </div>
                        
                        <div class="participant-section">
                            <span class="participant-text">报名人数：0/10 人</span>
                        </div>
                    </div>
                </div>
                <p style="text-align: center; margin: 10px 0; color: #999;">状态：未开始（隐藏按钮）</p>
            </div>
            
            <!-- 进行中状态 -->
            <div class="demo-item">
                <div class="activity-detail">
                    <div class="detail-content">
                        <div class="activity-title-section">
                            <div class="scrolling-title">
                                <span class="title-text">活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称</span>
                            </div>
                            <div class="activity-code">活动编号：000007</div>
                        </div>
                        
                        <div class="activity-header">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzc1IiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDM3NSAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzNzUiIGhlaWdodD0iMjAwIiBmaWxsPSIjRERERERGIi8+Cjx0ZXh0IHg9IjE4Ny41IiB5PSIxMDAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuS4u+WKqOWbvueJhzwvdGV4dD4KPC9zdmc+" alt="活动图片" />
                        </div>
                        
                        <div class="activity-status-section">
                            <div class="status-item">
                                <span class="status-label">活动状态：</span>
                                <span class="status-badge ongoing">进行中</span>
                            </div>
                        </div>
                        
                        <div class="activity-time-section">
                            <div class="time-item">
                                <span class="time-label">报名时间：</span>
                                <span class="time-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
                            </div>
                            <div class="time-item">
                                <span class="time-label">活动时间：</span>
                                <span class="time-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
                            </div>
                        </div>
                        
                        <div class="participant-section">
                            <span class="participant-text">报名人数：0/不限 人</span>
                        </div>
                    </div>
                    
                    <div class="bottom-actions">
                        <button class="register-button primary">一键报名</button>
                    </div>
                </div>
                <p style="text-align: center; margin: 10px 0; color: #999;">状态：进行中（显示一键报名按钮）</p>
            </div>
            
            <!-- 已结束状态 -->
            <div class="demo-item">
                <div class="activity-detail">
                    <div class="detail-content">
                        <div class="activity-title-section">
                            <div class="scrolling-title">
                                <span class="title-text">活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称</span>
                            </div>
                            <div class="activity-code">活动编号：000006</div>
                        </div>
                        
                        <div class="activity-header">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzc1IiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDM3NSAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzNzUiIGhlaWdodD0iMjAwIiBmaWxsPSIjRERERERGIi8+Cjx0ZXh0IHg9IjE4Ny41IiB5PSIxMDAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuS4u+WKqOWbvueJhzwvdGV4dD4KPC9zdmc+" alt="活动图片" />
                        </div>
                        
                        <div class="activity-status-section">
                            <div class="status-item">
                                <span class="status-label">活动状态：</span>
                                <span class="status-badge ended">已结束</span>
                            </div>
                        </div>
                        
                        <div class="activity-time-section">
                            <div class="time-item">
                                <span class="time-label">报名时间：</span>
                                <span class="time-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
                            </div>
                            <div class="time-item">
                                <span class="time-label">活动时间：</span>
                                <span class="time-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
                            </div>
                        </div>
                        
                        <div class="participant-section">
                            <span class="participant-text">报名人数：0/10 人</span>
                        </div>
                    </div>
                    
                    <div class="bottom-actions">
                        <button class="register-button registered">已报名</button>
                    </div>
                </div>
                <p style="text-align: center; margin: 10px 0; color: #999;">状态：已结束且已报名（显示已报名按钮）</p>
            </div>
        </div>
    </div>
</body>
</html>
